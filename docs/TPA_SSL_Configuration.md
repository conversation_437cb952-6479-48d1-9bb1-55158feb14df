# TPA API SSL Configuration

## Problem

The TPA (Third Party Administrator) API server at `uat.thirdpartyadmin.co.th:4443` has an expired SSL certificate, causing SSL verification failures when making API requests. This results in errors like:

```
SSLError(SSLCertificateVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1006)'))
```

## Solution

The TPA service has been updated to support configurable SSL verification through environment variables.

### Environment Variable Configuration

Add the following environment variable to your `.env` file:

```bash
# TPA API SSL Configuration
# Set to 'false' to disable SSL verification for expired certificates (development/testing only)
TPA_SSL_VERIFY=false
```

### Configuration Options

- `TPA_SSL_VERIFY=true` (default): Enable SSL certificate verification (recommended for production)
- `TPA_SSL_VERIFY=false`: Disable SSL certificate verification (for development/testing with expired certificates)

### Security Considerations

⚠️ **Warning**: Disabling SSL verification makes your application vulnerable to man-in-the-middle attacks. Only use `TPA_SSL_VERIFY=false` in development/testing environments with expired certificates.

For production environments:
1. Contact the TPA API provider to renew their SSL certificate
2. Use `TPA_SSL_VERIFY=true` (default)
3. Consider using a custom CA bundle if needed

### Implementation Details

The TPA service (`customer/_services/tpa_service.py`) now:

1. Reads the `TPA_SSL_VERIFY` environment variable on initialization
2. Configures the requests session to use the specified SSL verification setting
3. Logs a warning when SSL verification is disabled
4. Disables urllib3 SSL warnings when verification is disabled to reduce log noise

### Testing

To test the SSL configuration:

```bash
python test_tpa_ssl.py
```

This script will:
1. Test SSL verification with different environment variable settings
2. Attempt an actual API call to verify the SSL issue is resolved
3. Report whether the configuration is working correctly

### Usage in Code

The SSL configuration is automatically applied to all TPA API requests through the `TPAApiService` class. No code changes are required in the policy workflow service or other components that use the TPA service.

### Troubleshooting

If you're still experiencing SSL errors after setting `TPA_SSL_VERIFY=false`:

1. Verify the environment variable is correctly set in your `.env` file
2. Restart your Django application to reload environment variables
3. Check the application logs for SSL-related warnings
4. Run the test script to verify the configuration

### Related Files

- `customer/_services/tpa_service.py` - TPA service implementation
- `customer/_services/policy_workflow_service.py` - Policy workflow service that uses TPA service
- `.env` - Environment configuration file
- `test_tpa_ssl.py` - SSL configuration test script
