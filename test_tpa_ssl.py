#!/usr/bin/env python3
"""
Test script to verify TPA SSL configuration works
"""
import os
import sys
import django

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'devproject.settings')
django.setup()

from customer._services.tpa_service import TPAApiService

def test_tpa_ssl_config():
    """Test TPA SSL configuration"""
    print("Testing TPA SSL configuration...")
    
    # Test with SSL verification enabled (default)
    print("\n1. Testing with SSL verification enabled (default):")
    os.environ.pop('TPA_SSL_VERIFY', None)  # Remove if exists
    service1 = TPAApiService()
    print(f"   SSL verification: {service1.verify_ssl}")
    
    # Test with SSL verification disabled
    print("\n2. Testing with SSL verification disabled:")
    os.environ['TPA_SSL_VERIFY'] = 'false'
    service2 = TPAApiService()
    print(f"   SSL verification: {service2.verify_ssl}")
    
    # Test with SSL verification explicitly enabled
    print("\n3. Testing with SSL verification explicitly enabled:")
    os.environ['TPA_SSL_VERIFY'] = 'true'
    service3 = TPAApiService()
    print(f"   SSL verification: {service3.verify_ssl}")
    
    print("\nTPA SSL configuration test completed successfully!")
    
    # Test a simple request with SSL disabled to see if it resolves the certificate issue
    print("\n4. Testing actual API call with SSL disabled:")
    os.environ['TPA_SSL_VERIFY'] = 'false'
    service_test = TPAApiService()

    try:
        # Try to make a simple request to test connectivity
        # This should fail with authentication error but not SSL error
        token = service_test.get_bearer_token("test_social_id", "test_channel_id", "LINE")
        print(f"   Unexpected success: {token}")
    except Exception as e:
        error_msg = str(e)
        if "SSL" in error_msg or "certificate" in error_msg.lower():
            print(f"   ❌ SSL error still present: {error_msg}")
            return False
        else:
            print(f"   ✅ SSL error resolved, got different error (expected): {error_msg}")
            return True

    return True

if __name__ == "__main__":
    success = test_tpa_ssl_config()
    if success:
        print("\n🎉 SSL configuration test completed successfully!")
        print("The TPA SSL certificate issue should now be resolved.")
        print("You can now test the policy workflow endpoints.")
    else:
        print("\n❌ SSL configuration test failed.")
        print("The certificate issue may still persist.")
