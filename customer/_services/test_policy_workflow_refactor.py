"""
Test script to verify the refactored PolicyWorkflowService works correctly
"""
import os
import sys
import django
import json
from unittest.mock import Mock, patch, MagicMock

# Add the project root to Python path
sys.path.append('/Users/<USER>/Developer/Salmate')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'salmate.settings')
django.setup()

from customer._services.policy_workflow_service import (
    WorkflowConfigLoader,
    WorkflowTemplateResolver,
    WorkflowDataExtractor,
    WorkflowValidator,
    GenericWorkflowExecutor,
    PolicyWorkflowService
)
from customer._services.tpa_service import TPAApiService
from customer.models import Customer, CustomerPlatformIdentity


def test_workflow_config_loader():
    """Test workflow configuration loading"""
    print("Testing WorkflowConfigLoader...")
    
    try:
        # Test loading policy list workflow
        config = WorkflowConfigLoader.load_workflow_config('POLICY_LIST')
        assert 'steps' in config
        assert 'config' in config
        assert len(config['steps']) > 0
        print("✅ Policy list workflow config loaded successfully")
        
        # Test loading policy details workflow
        config = WorkflowConfigLoader.load_workflow_config('POLICY_DETAILS')
        assert 'steps' in config
        assert 'config' in config
        assert len(config['steps']) > 0
        print("✅ Policy details workflow config loaded successfully")
        
        # Test invalid workflow type
        try:
            WorkflowConfigLoader.load_workflow_config('INVALID')
            assert False, "Should have raised an error"
        except Exception as e:
            print("✅ Invalid workflow type properly handled")
        
        return True
    except Exception as e:
        print(f"❌ WorkflowConfigLoader test failed: {str(e)}")
        return False


def test_template_resolver():
    """Test template variable resolution"""
    print("Testing WorkflowTemplateResolver...")
    
    try:
        context = {
            'social_id': 'U123456789',
            'channel_id': '2006769099',
            'bearer_token': 'test_token_123'
        }
        
        # Test simple template resolution
        template = "Bearer {{bearer_token}}"
        resolved = WorkflowTemplateResolver.resolve_template_variables(template, context)
        assert resolved == "Bearer test_token_123"
        print("✅ Simple template resolution works")
        
        # Test request data resolution
        request_data = {
            'SOCIAL_ID': '{{social_id}}',
            'CHANNEL_ID': '{{channel_id}}',
            'STATIC_VALUE': 'test'
        }
        resolved_data = WorkflowTemplateResolver.resolve_request_data(request_data, context)
        assert resolved_data['SOCIAL_ID'] == 'U123456789'
        assert resolved_data['CHANNEL_ID'] == '2006769099'
        assert resolved_data['STATIC_VALUE'] == 'test'
        print("✅ Request data resolution works")
        
        return True
    except Exception as e:
        print(f"❌ WorkflowTemplateResolver test failed: {str(e)}")
        return False


def test_data_extractor():
    """Test data extraction from API responses"""
    print("Testing WorkflowDataExtractor...")
    
    try:
        # Mock API response
        response = {
            'ListOfPolicyListSocial': [
                {'MemberCode': 'M001', 'PolicyName': 'Policy 1'},
                {'MemberCode': 'M002', 'PolicyName': 'Policy 2'}
            ],
            'Status': 'SUCCESS'
        }
        
        # Test extraction configuration
        extract_config = {
            'policies': '$.ListOfPolicyListSocial',
            'member_codes': '$.ListOfPolicyListSocial[*].MemberCode',
            'status': '$.Status'
        }
        
        extracted = WorkflowDataExtractor.extract_data(response, extract_config)
        assert len(extracted['policies']) == 2
        assert extracted['member_codes'] == ['M001', 'M002']
        assert extracted['status'] == 'SUCCESS'
        print("✅ Data extraction works correctly")
        
        return True
    except Exception as e:
        print(f"❌ WorkflowDataExtractor test failed: {str(e)}")
        return False


def test_validator():
    """Test response validation"""
    print("Testing WorkflowValidator...")
    
    try:
        # Test citizen ID validation - success case
        response = {
            'ListOfSearchCitizenID': [{'Status': '1', 'CitizenID': '1234567890123'}]
        }
        result = WorkflowValidator.validate_response(response, "$.ListOfSearchCitizenID[0].Status == '1'")
        assert result == True
        print("✅ Citizen ID validation (success) works")
        
        # Test registration validation - success case
        response = {
            'ListOfCheckRegister': [{'Status': 'YES', 'Registered': True}]
        }
        result = WorkflowValidator.validate_response(response, "$.ListOfCheckRegister[0].Status == 'YES'")
        assert result == True
        print("✅ Registration validation (success) works")
        
        return True
    except Exception as e:
        print(f"❌ WorkflowValidator test failed: {str(e)}")
        return False


def test_tpa_service_dynamic_requests():
    """Test TPA service dynamic request functionality"""
    print("Testing TPAApiService dynamic requests...")

    try:
        from unittest.mock import Mock, patch

        # Mock the TPA service
        tpa_service = TPAApiService()

        # Mock the _make_request_with_retry method
        with patch.object(tpa_service, '_make_request_with_retry') as mock_request:
            # Test token request
            mock_response = Mock()
            mock_response.text = '"test_token_123"'
            mock_response.headers = {'content-type': 'text/plain'}
            mock_request.return_value = mock_response

            payload = {
                'USERNAME': 'BVTPA',
                'PASSWORD': '*d!n^+Cb@1',
                'SOCIAL_ID': 'U123456789',
                'CHANNEL_ID': '2006769099',
                'CHANNEL': 'LINE'
            }
            headers = {'Content-Type': 'application/x-www-form-urlencoded'}

            result = tpa_service.make_dynamic_request('/api/GetToken', 'POST', payload, headers)
            assert result == 'test_token_123'
            print("✅ Dynamic token request works")

            # Test JSON request
            mock_response.json.return_value = {'Status': 'SUCCESS', 'Data': []}
            mock_response.headers = {'content-type': 'application/json'}

            payload = {'CitizenID': '1234567890123'}
            headers = {'Authorization': 'Bearer test_token_123'}

            result = tpa_service.make_dynamic_request('/api/SearchCitizenID', 'POST', payload, headers)
            assert result['Status'] == 'SUCCESS'
            print("✅ Dynamic JSON request works")

        return True
    except Exception as e:
        print(f"❌ TPAApiService dynamic requests test failed: {str(e)}")
        return False


def test_generic_workflow_executor():
    """Test the generic workflow executor with mocked TPA service"""
    print("Testing GenericWorkflowExecutor...")

    try:
        from unittest.mock import Mock, patch

        # Mock TPA service
        mock_tpa_service = Mock()
        mock_tpa_service.make_dynamic_request.side_effect = [
            'test_token_123',  # Token request
            {'ListOfSearchCitizenID': [{'Status': '1'}]},  # Citizen verification
            {'ListOfCheckRegister': [{'Status': 'YES'}]},  # Registration check
            {'ListOfPolicyListSocial': [{'MemberCode': 'M001'}]}  # Policy list
        ]

        # Create workflow executor
        executor = GenericWorkflowExecutor(mock_tpa_service)

        # Mock customer and platform identity
        mock_customer = Mock()
        mock_customer.customer_id = 123
        mock_customer.national_id = '1234567890123'
        mock_customer.get_identity_for_platform.return_value = Mock(
            platform_user_id='U123456789',
            channel_id='2006769099',
            platform='LINE'
        )

        context = {
            'customer': mock_customer,
            'execution_id': 'test_execution_123'
        }

        # Mock the workflow config loading
        with patch.object(WorkflowConfigLoader, 'load_workflow_config') as mock_config:
            mock_config.return_value = {
                'config': {
                    'mode': 'database'
                },
                'steps': [
                    {
                        'id': 1,
                        'name': 'get_bearer_token',
                        'endpoint': '/api/GetToken',
                        'method': 'POST',
                        'request': {
                            'USERNAME': 'BVTPA',
                            'PASSWORD': '*d!n^+Cb@1',
                            'SOCIAL_ID': '{{social_id}}',
                            'CHANNEL_ID': '{{channel_id}}',
                            'CHANNEL': '{{channel}}'
                        },
                        'extract': {
                            'bearer_token': '$'
                        },
                        'retry': 3
                    }
                ]
            }

            result = executor.execute_workflow('POLICY_LIST', context)
            assert 'step_results' in result
            assert 'step_data' in result
            assert result['step_data']['bearer_token'] == 'test_token_123'
            print("✅ Generic workflow executor works")

        return True
    except Exception as e:
        print(f"❌ GenericWorkflowExecutor test failed: {str(e)}")
        return False


def run_all_tests():
    """Run all tests"""
    print("🧪 Running Policy Workflow Refactor Tests\n")

    tests = [
        test_workflow_config_loader,
        test_template_resolver,
        test_data_extractor,
        test_validator,
        test_tpa_service_dynamic_requests,
        test_generic_workflow_executor
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1
        print()  # Add spacing between tests

    print(f"📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! The refactored service is working correctly.")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")

    return passed == total


if __name__ == "__main__":
    run_all_tests()
