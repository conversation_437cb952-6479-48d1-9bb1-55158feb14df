import time
import logging
from django.core.cache import cache
from django.db.models import Count, Avg
from django.utils import timezone
from datetime import timedelta
from typing import Dict, Any

from customer.models import PolicyWorkflowAuditLog

logger = logging.getLogger(__name__)

class WorkflowMonitoringService:
    """Service for monitoring workflow performance and health"""

    @classmethod
    def record_workflow_execution(cls, execution_data: Dict[str, Any]):
        """Record workflow execution metrics"""
        try:
            # Store in audit log
            PolicyWorkflowAuditLog.objects.create(**execution_data)

            # Update real-time metrics in cache
            cache_key = f"workflow_metrics_{execution_data['workflow_type']}"
            metrics = cache.get(cache_key, {
                'total_executions': 0,
                'successful_executions': 0,
                'average_execution_time': 0,
                'last_updated': time.time()
            })

            metrics['total_executions'] += 1
            if execution_data['success']:
                metrics['successful_executions'] += 1

            # Update average execution time
            current_avg = metrics['average_execution_time']
            new_time = execution_data['total_execution_time_ms']
            metrics['average_execution_time'] = (
                (current_avg * (metrics['total_executions'] - 1) + new_time) /
                metrics['total_executions']
            )

            metrics['last_updated'] = time.time()
            cache.set(cache_key, metrics, 3600)  # Cache for 1 hour

        except Exception as e:
            logger.error(f"Failed to record workflow metrics: {str(e)}")

    @classmethod
    def get_workflow_health_status(cls) -> Dict[str, Any]:
        """Get current workflow system health status"""
        try:
            # Get recent execution statistics
            recent_logs = PolicyWorkflowAuditLog.objects.filter(
                created_on__gte=timezone.now() - timedelta(hours=1)
            )

            total_executions = recent_logs.count()
            successful_executions = recent_logs.filter(success=True).count()

            success_rate = (successful_executions / total_executions * 100) if total_executions > 0 else 0

            avg_execution_time = recent_logs.aggregate(
                avg_time=Avg('total_execution_time_ms')
            )['avg_time'] or 0

            return {
                'status': 'healthy' if success_rate >= 95 else 'degraded' if success_rate >= 80 else 'unhealthy',
                'success_rate': round(success_rate, 2),
                'total_executions_last_hour': total_executions,
                'average_execution_time_ms': round(avg_execution_time, 2),
                'last_updated': timezone.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get workflow health status: {str(e)}")
            return {
                'status': 'unknown',
                'error': str(e),
                'last_updated': timezone.now().isoformat()
            }
