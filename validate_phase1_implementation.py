#!/usr/bin/env python3
"""
Validation script for Phase 1 Policy Workflow Migration Implementation
This script validates that all components are properly implemented and configured.
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'devproject.settings')
django.setup()

def validate_models():
    """Validate that new models are properly defined"""
    print("🔍 Validating models...")
    
    try:
        from customer.models import CustomerPolicyWorkflowCache, PolicyWorkflowAuditLog
        print("✅ CustomerPolicyWorkflowCache model imported successfully")
        print("✅ PolicyWorkflowAuditLog model imported successfully")
        
        # Check model fields
        cache_fields = [f.name for f in CustomerPolicyWorkflowCache._meta.fields]
        expected_cache_fields = ['customer', 'workflow_type', 'member_code', 'citizen_id', 
                               'social_id', 'channel_id', 'raw_response_data', 'processed_data',
                               'execution_id', 'execution_time_ms', 'success', 'error_message',
                               'created_on', 'expires_at']
        
        for field in expected_cache_fields:
            if field in cache_fields:
                print(f"✅ CustomerPolicyWorkflowCache.{field} field exists")
            else:
                print(f"❌ CustomerPolicyWorkflowCache.{field} field missing")
        
        return True
    except ImportError as e:
        print(f"❌ Model import failed: {e}")
        return False

def validate_services():
    """Validate that services are properly implemented"""
    print("\n🔍 Validating services...")
    
    try:
        from customer._services.tpa_service import TPAApiService
        from customer._services.policy_workflow_service import PolicyWorkflowService
        print("✅ TPAApiService imported successfully")
        print("✅ PolicyWorkflowService imported successfully")
        
        # Check service methods
        tpa_methods = ['get_bearer_token', 'verify_citizen_id', 'check_registration', 
                      'get_policy_list', 'get_policy_details']
        
        for method in tpa_methods:
            if hasattr(TPAApiService, method):
                print(f"✅ TPAApiService.{method} method exists")
            else:
                print(f"❌ TPAApiService.{method} method missing")
        
        workflow_methods = ['execute_policy_list_workflow', 'execute_policy_details_workflow']
        
        for method in workflow_methods:
            if hasattr(PolicyWorkflowService, method):
                print(f"✅ PolicyWorkflowService.{method} method exists")
            else:
                print(f"❌ PolicyWorkflowService.{method} method missing")
        
        return True
    except ImportError as e:
        print(f"❌ Service import failed: {e}")
        return False

def validate_views():
    """Validate that API views are properly implemented"""
    print("\n🔍 Validating views...")
    
    try:
        from customer.views import CustomerPolicyListWorkflowView, CustomerPolicyDetailsWorkflowView
        print("✅ CustomerPolicyListWorkflowView imported successfully")
        print("✅ CustomerPolicyDetailsWorkflowView imported successfully")
        
        # Check view methods
        for view_class in [CustomerPolicyListWorkflowView, CustomerPolicyDetailsWorkflowView]:
            if hasattr(view_class, 'get'):
                print(f"✅ {view_class.__name__}.get method exists")
            else:
                print(f"❌ {view_class.__name__}.get method missing")
        
        return True
    except ImportError as e:
        print(f"❌ View import failed: {e}")
        return False

def validate_urls():
    """Validate that URL patterns are properly configured"""
    print("\n🔍 Validating URLs...")
    
    try:
        from django.urls import reverse
        
        # Test URL patterns
        policy_list_url = reverse('customer-policy-list-workflow', args=[1])
        policy_details_url = reverse('customer-policy-details-workflow', args=[1, 'TEST-001'])
        
        print(f"✅ Policy list workflow URL: {policy_list_url}")
        print(f"✅ Policy details workflow URL: {policy_details_url}")
        
        return True
    except Exception as e:
        print(f"❌ URL validation failed: {e}")
        return False

def validate_migration():
    """Validate that migration file exists"""
    print("\n🔍 Validating migration...")
    
    migration_file = project_root / "customer" / "migrations" / "0009_add_policy_workflow_models.py"
    
    if migration_file.exists():
        print("✅ Migration file exists")
        return True
    else:
        print("❌ Migration file missing")
        return False

def main():
    """Run all validations"""
    print("🚀 Starting Phase 1 Implementation Validation\n")
    
    validations = [
        validate_models,
        validate_services,
        validate_views,
        validate_urls,
        validate_migration
    ]
    
    results = []
    for validation in validations:
        results.append(validation())
    
    print("\n" + "="*50)
    print("📊 VALIDATION SUMMARY")
    print("="*50)
    
    if all(results):
        print("🎉 All validations passed! Phase 1 implementation is complete.")
        return 0
    else:
        failed_count = len([r for r in results if not r])
        print(f"❌ {failed_count} validation(s) failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
